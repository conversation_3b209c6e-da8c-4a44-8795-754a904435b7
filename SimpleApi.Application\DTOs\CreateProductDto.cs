using System.ComponentModel.DataAnnotations;

namespace SimpleApi.Application.DTOs;

/// <summary>
/// Data Transfer Object for creating a new Product
/// </summary>
public class CreateProductDto
{
    /// <summary>
    /// Gets or sets the product name
    /// </summary>
    [Required(ErrorMessage = "Product name is required")]
    [StringLength(200, ErrorMessage = "Product name cannot exceed 200 characters")]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the product description
    /// </summary>
    [StringLength(1000, ErrorMessage = "Description cannot exceed 1000 characters")]
    public string? Description { get; set; }

    /// <summary>
    /// Gets or sets the product price
    /// </summary>
    [Required(ErrorMessage = "Price is required")]
    [Range(0.01, double.MaxValue, ErrorMessage = "Price must be greater than 0")]
    public decimal Price { get; set; }

    /// <summary>
    /// Gets or sets the product SKU
    /// </summary>
    [Required(ErrorMessage = "SKU is required")]
    [StringLength(50, ErrorMessage = "SKU cannot exceed 50 characters")]
    public string Sku { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the stock quantity
    /// </summary>
    [Range(0, int.MaxValue, ErrorMessage = "Stock quantity must be non-negative")]
    public int StockQuantity { get; set; }

    /// <summary>
    /// Gets or sets the category ID
    /// </summary>
    [Required(ErrorMessage = "Category ID is required")]
    public Guid CategoryId { get; set; }
}
