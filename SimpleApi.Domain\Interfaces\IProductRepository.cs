using SimpleApi.Domain.Entities;

namespace SimpleApi.Domain.Interfaces;

/// <summary>
/// Product repository interface
/// </summary>
public interface IProductRepository : IRepository<Product>
{
    /// <summary>
    /// Gets products by category ID
    /// </summary>
    /// <param name="categoryId">Category ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of products in the specified category</returns>
    Task<IEnumerable<Product>> GetByCategoryAsync(Guid categoryId, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a product by its SKU
    /// </summary>
    /// <param name="sku">Product SKU</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Product with the specified SKU or null if not found</returns>
    Task<Product?> GetBySkuAsync(string sku, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets products with low stock (below specified threshold)
    /// </summary>
    /// <param name="threshold">Stock threshold</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of products with low stock</returns>
    Task<IEnumerable<Product>> GetLowStockProductsAsync(int threshold, CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets products with their categories included
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of products with categories</returns>
    Task<IEnumerable<Product>> GetWithCategoriesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a product by ID with category included
    /// </summary>
    /// <param name="id">Product ID</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Product with category or null if not found</returns>
    Task<Product?> GetByIdWithCategoryAsync(Guid id, CancellationToken cancellationToken = default);
}
