using System.ComponentModel.DataAnnotations;

namespace SimpleApi.Domain.Entities;

/// <summary>
/// Represents a product entity
/// </summary>
public class Product : BaseEntity
{
    /// <summary>
    /// Gets or sets the product name
    /// </summary>
    [Required]
    [MaxLength(200)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the product description
    /// </summary>
    [MaxLength(1000)]
    public string? Description { get; set; }

    /// <summary>
    /// Gets or sets the product price
    /// </summary>
    [Range(0, double.MaxValue, ErrorMessage = "Price must be a positive value")]
    public decimal Price { get; set; }

    /// <summary>
    /// Gets or sets the product SKU (Stock Keeping Unit)
    /// </summary>
    [Required]
    [MaxLength(50)]
    public string Sku { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the quantity in stock
    /// </summary>
    [Range(0, int.MaxValue, ErrorMessage = "Stock quantity must be a non-negative value")]
    public int StockQuantity { get; set; }

    /// <summary>
    /// Gets or sets the category ID
    /// </summary>
    public Guid CategoryId { get; set; }

    /// <summary>
    /// Gets or sets the category navigation property
    /// </summary>
    public virtual Category Category { get; set; } = null!;
}
