{"version": 2, "dgSpecHash": "/W+ArRsycI0=", "success": true, "projectFilePath": "E:\\temp\\simpleApi\\SimpleApi.Api\\SimpleApi.Api.csproj", "expectedPackageFiles": ["E:\\Install\\Nuget\\azure.core\\1.38.0\\azure.core.1.38.0.nupkg.sha512", "E:\\Install\\Nuget\\azure.identity\\1.11.4\\azure.identity.1.11.4.nupkg.sha512", "E:\\Install\\Nuget\\microsoft.aspnetcore.openapi\\9.0.4\\microsoft.aspnetcore.openapi.9.0.4.nupkg.sha512", "E:\\Install\\Nuget\\microsoft.bcl.asyncinterfaces\\1.1.1\\microsoft.bcl.asyncinterfaces.1.1.1.nupkg.sha512", "E:\\Install\\Nuget\\microsoft.csharp\\4.5.0\\microsoft.csharp.4.5.0.nupkg.sha512", "E:\\Install\\Nuget\\microsoft.data.sqlclient\\5.1.6\\microsoft.data.sqlclient.5.1.6.nupkg.sha512", "E:\\Install\\Nuget\\microsoft.data.sqlclient.sni.runtime\\5.1.1\\microsoft.data.sqlclient.sni.runtime.5.1.1.nupkg.sha512", "E:\\Install\\Nuget\\microsoft.entityframeworkcore\\9.0.7\\microsoft.entityframeworkcore.9.0.7.nupkg.sha512", "E:\\Install\\Nuget\\microsoft.entityframeworkcore.abstractions\\9.0.7\\microsoft.entityframeworkcore.abstractions.9.0.7.nupkg.sha512", "E:\\Install\\Nuget\\microsoft.entityframeworkcore.analyzers\\9.0.7\\microsoft.entityframeworkcore.analyzers.9.0.7.nupkg.sha512", "E:\\Install\\Nuget\\microsoft.entityframeworkcore.relational\\9.0.7\\microsoft.entityframeworkcore.relational.9.0.7.nupkg.sha512", "E:\\Install\\Nuget\\microsoft.entityframeworkcore.sqlserver\\9.0.7\\microsoft.entityframeworkcore.sqlserver.9.0.7.nupkg.sha512", "E:\\Install\\Nuget\\microsoft.extensions.caching.abstractions\\9.0.7\\microsoft.extensions.caching.abstractions.9.0.7.nupkg.sha512", "E:\\Install\\Nuget\\microsoft.extensions.caching.memory\\9.0.7\\microsoft.extensions.caching.memory.9.0.7.nupkg.sha512", "E:\\Install\\Nuget\\microsoft.extensions.configuration.abstractions\\9.0.7\\microsoft.extensions.configuration.abstractions.9.0.7.nupkg.sha512", "E:\\Install\\Nuget\\microsoft.extensions.dependencyinjection\\9.0.7\\microsoft.extensions.dependencyinjection.9.0.7.nupkg.sha512", "E:\\Install\\Nuget\\microsoft.extensions.dependencyinjection.abstractions\\9.0.7\\microsoft.extensions.dependencyinjection.abstractions.9.0.7.nupkg.sha512", "E:\\Install\\Nuget\\microsoft.extensions.logging\\9.0.7\\microsoft.extensions.logging.9.0.7.nupkg.sha512", "E:\\Install\\Nuget\\microsoft.extensions.logging.abstractions\\9.0.7\\microsoft.extensions.logging.abstractions.9.0.7.nupkg.sha512", "E:\\Install\\Nuget\\microsoft.extensions.options\\9.0.7\\microsoft.extensions.options.9.0.7.nupkg.sha512", "E:\\Install\\Nuget\\microsoft.extensions.primitives\\9.0.7\\microsoft.extensions.primitives.9.0.7.nupkg.sha512", "E:\\Install\\Nuget\\microsoft.identity.client\\4.61.3\\microsoft.identity.client.4.61.3.nupkg.sha512", "E:\\Install\\Nuget\\microsoft.identity.client.extensions.msal\\4.61.3\\microsoft.identity.client.extensions.msal.4.61.3.nupkg.sha512", "E:\\Install\\Nuget\\microsoft.identitymodel.abstractions\\6.35.0\\microsoft.identitymodel.abstractions.6.35.0.nupkg.sha512", "E:\\Install\\Nuget\\microsoft.identitymodel.jsonwebtokens\\6.35.0\\microsoft.identitymodel.jsonwebtokens.6.35.0.nupkg.sha512", "E:\\Install\\Nuget\\microsoft.identitymodel.logging\\6.35.0\\microsoft.identitymodel.logging.6.35.0.nupkg.sha512", "E:\\Install\\Nuget\\microsoft.identitymodel.protocols\\6.35.0\\microsoft.identitymodel.protocols.6.35.0.nupkg.sha512", "E:\\Install\\Nuget\\microsoft.identitymodel.protocols.openidconnect\\6.35.0\\microsoft.identitymodel.protocols.openidconnect.6.35.0.nupkg.sha512", "E:\\Install\\Nuget\\microsoft.identitymodel.tokens\\6.35.0\\microsoft.identitymodel.tokens.6.35.0.nupkg.sha512", "E:\\Install\\Nuget\\microsoft.netcore.platforms\\1.1.0\\microsoft.netcore.platforms.1.1.0.nupkg.sha512", "E:\\Install\\Nuget\\microsoft.netcore.targets\\1.1.0\\microsoft.netcore.targets.1.1.0.nupkg.sha512", "E:\\Install\\Nuget\\microsoft.openapi\\1.6.17\\microsoft.openapi.1.6.17.nupkg.sha512", "E:\\Install\\Nuget\\microsoft.sqlserver.server\\1.0.0\\microsoft.sqlserver.server.1.0.0.nupkg.sha512", "E:\\Install\\Nuget\\microsoft.win32.systemevents\\6.0.0\\microsoft.win32.systemevents.6.0.0.nupkg.sha512", "E:\\Install\\Nuget\\system.clientmodel\\1.0.0\\system.clientmodel.1.0.0.nupkg.sha512", "E:\\Install\\Nuget\\system.configuration.configurationmanager\\6.0.1\\system.configuration.configurationmanager.6.0.1.nupkg.sha512", "E:\\Install\\Nuget\\system.diagnostics.diagnosticsource\\6.0.1\\system.diagnostics.diagnosticsource.6.0.1.nupkg.sha512", "E:\\Install\\Nuget\\system.drawing.common\\6.0.0\\system.drawing.common.6.0.0.nupkg.sha512", "E:\\Install\\Nuget\\system.formats.asn1\\9.0.7\\system.formats.asn1.9.0.7.nupkg.sha512", "E:\\Install\\Nuget\\system.identitymodel.tokens.jwt\\6.35.0\\system.identitymodel.tokens.jwt.6.35.0.nupkg.sha512", "E:\\Install\\Nuget\\system.memory\\4.5.4\\system.memory.4.5.4.nupkg.sha512", "E:\\Install\\Nuget\\system.memory.data\\1.0.2\\system.memory.data.1.0.2.nupkg.sha512", "E:\\Install\\Nuget\\system.numerics.vectors\\4.5.0\\system.numerics.vectors.4.5.0.nupkg.sha512", "E:\\Install\\Nuget\\system.runtime\\4.3.0\\system.runtime.4.3.0.nupkg.sha512", "E:\\Install\\Nuget\\system.runtime.caching\\6.0.0\\system.runtime.caching.6.0.0.nupkg.sha512", "E:\\Install\\Nuget\\system.runtime.compilerservices.unsafe\\6.0.0\\system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512", "E:\\Install\\Nuget\\system.security.accesscontrol\\6.0.0\\system.security.accesscontrol.6.0.0.nupkg.sha512", "E:\\Install\\Nuget\\system.security.cryptography.cng\\5.0.0\\system.security.cryptography.cng.5.0.0.nupkg.sha512", "E:\\Install\\Nuget\\system.security.cryptography.protecteddata\\6.0.0\\system.security.cryptography.protecteddata.6.0.0.nupkg.sha512", "E:\\Install\\Nuget\\system.security.permissions\\6.0.0\\system.security.permissions.6.0.0.nupkg.sha512", "E:\\Install\\Nuget\\system.security.principal.windows\\5.0.0\\system.security.principal.windows.5.0.0.nupkg.sha512", "E:\\Install\\Nuget\\system.text.encoding\\4.3.0\\system.text.encoding.4.3.0.nupkg.sha512", "E:\\Install\\Nuget\\system.text.encoding.codepages\\6.0.0\\system.text.encoding.codepages.6.0.0.nupkg.sha512", "E:\\Install\\Nuget\\system.text.encodings.web\\6.0.0\\system.text.encodings.web.6.0.0.nupkg.sha512", "E:\\Install\\Nuget\\system.text.json\\9.0.7\\system.text.json.9.0.7.nupkg.sha512", "E:\\Install\\Nuget\\system.threading.tasks.extensions\\4.5.4\\system.threading.tasks.extensions.4.5.4.nupkg.sha512", "E:\\Install\\Nuget\\system.windows.extensions\\6.0.0\\system.windows.extensions.6.0.0.nupkg.sha512"], "logs": []}