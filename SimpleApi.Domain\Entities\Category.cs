using System.ComponentModel.DataAnnotations;

namespace SimpleApi.Domain.Entities;

/// <summary>
/// Represents a product category entity
/// </summary>
public class Category : BaseEntity
{
    /// <summary>
    /// Gets or sets the category name
    /// </summary>
    [Required]
    [MaxLength(100)]
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the category description
    /// </summary>
    [MaxLength(500)]
    public string? Description { get; set; }

    /// <summary>
    /// Gets or sets the collection of products in this category
    /// </summary>
    public virtual ICollection<Product> Products { get; set; } = new List<Product>();
}
