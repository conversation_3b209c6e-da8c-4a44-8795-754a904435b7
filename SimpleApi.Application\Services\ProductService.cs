using SimpleApi.Application.DTOs;
using SimpleApi.Domain.Entities;
using SimpleApi.Domain.Interfaces;

namespace SimpleApi.Application.Services;

/// <summary>
/// Product service implementation
/// </summary>
public class ProductService : IProductService
{
    private readonly IUnitOfWork _unitOfWork;

    /// <summary>
    /// Initializes a new instance of the <see cref="ProductService"/> class
    /// </summary>
    /// <param name="unitOfWork">Unit of work</param>
    public ProductService(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    /// <inheritdoc />
    public async Task<IEnumerable<ProductDto>> GetAllProductsAsync(CancellationToken cancellationToken = default)
    {
        var products = await _unitOfWork.Products.GetWithCategoriesAsync(cancellationToken);
        return products.Select(MapToDto);
    }

    /// <inheritdoc />
    public async Task<ProductDto?> GetProductByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var product = await _unitOfWork.Products.GetByIdWithCategoryAsync(id, cancellationToken);
        return product != null ? MapToDto(product) : null;
    }

    /// <inheritdoc />
    public async Task<IEnumerable<ProductDto>> GetProductsByCategoryAsync(Guid categoryId, CancellationToken cancellationToken = default)
    {
        var products = await _unitOfWork.Products.GetByCategoryAsync(categoryId, cancellationToken);
        return products.Select(MapToDto);
    }

    /// <inheritdoc />
    public async Task<ProductDto?> GetProductBySkuAsync(string sku, CancellationToken cancellationToken = default)
    {
        var product = await _unitOfWork.Products.GetBySkuAsync(sku, cancellationToken);
        return product != null ? MapToDto(product) : null;
    }

    /// <inheritdoc />
    public async Task<ProductDto> CreateProductAsync(CreateProductDto createProductDto, CancellationToken cancellationToken = default)
    {
        // Check if category exists
        var category = await _unitOfWork.Categories.GetByIdAsync(createProductDto.CategoryId, cancellationToken);
        if (category == null)
        {
            throw new ArgumentException($"Category with ID {createProductDto.CategoryId} not found.");
        }

        // Check if SKU already exists
        var existingProduct = await _unitOfWork.Products.GetBySkuAsync(createProductDto.Sku, cancellationToken);
        if (existingProduct != null)
        {
            throw new ArgumentException($"Product with SKU '{createProductDto.Sku}' already exists.");
        }

        var product = new Product
        {
            Name = createProductDto.Name,
            Description = createProductDto.Description,
            Price = createProductDto.Price,
            Sku = createProductDto.Sku,
            StockQuantity = createProductDto.StockQuantity,
            CategoryId = createProductDto.CategoryId
        };

        await _unitOfWork.Products.AddAsync(product, cancellationToken);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // Reload with category
        var createdProduct = await _unitOfWork.Products.GetByIdWithCategoryAsync(product.Id, cancellationToken);
        return MapToDto(createdProduct!);
    }

    /// <inheritdoc />
    public async Task<ProductDto?> UpdateProductAsync(Guid id, UpdateProductDto updateProductDto, CancellationToken cancellationToken = default)
    {
        var product = await _unitOfWork.Products.GetByIdAsync(id, cancellationToken);
        if (product == null)
        {
            return null;
        }

        // Check if category exists
        var category = await _unitOfWork.Categories.GetByIdAsync(updateProductDto.CategoryId, cancellationToken);
        if (category == null)
        {
            throw new ArgumentException($"Category with ID {updateProductDto.CategoryId} not found.");
        }

        // Check if SKU already exists for another product
        var existingProduct = await _unitOfWork.Products.GetBySkuAsync(updateProductDto.Sku, cancellationToken);
        if (existingProduct != null && existingProduct.Id != id)
        {
            throw new ArgumentException($"Product with SKU '{updateProductDto.Sku}' already exists.");
        }

        product.Name = updateProductDto.Name;
        product.Description = updateProductDto.Description;
        product.Price = updateProductDto.Price;
        product.Sku = updateProductDto.Sku;
        product.StockQuantity = updateProductDto.StockQuantity;
        product.CategoryId = updateProductDto.CategoryId;

        _unitOfWork.Products.Update(product);
        await _unitOfWork.SaveChangesAsync(cancellationToken);

        // Reload with category
        var updatedProduct = await _unitOfWork.Products.GetByIdWithCategoryAsync(product.Id, cancellationToken);
        return MapToDto(updatedProduct!);
    }

    /// <inheritdoc />
    public async Task<bool> DeleteProductAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var product = await _unitOfWork.Products.GetByIdAsync(id, cancellationToken);
        if (product == null)
        {
            return false;
        }

        _unitOfWork.Products.Remove(product);
        await _unitOfWork.SaveChangesAsync(cancellationToken);
        return true;
    }

    /// <inheritdoc />
    public async Task<IEnumerable<ProductDto>> GetLowStockProductsAsync(int threshold = 10, CancellationToken cancellationToken = default)
    {
        var products = await _unitOfWork.Products.GetLowStockProductsAsync(threshold, cancellationToken);
        return products.Select(MapToDto);
    }

    /// <summary>
    /// Maps a Product entity to a ProductDto
    /// </summary>
    /// <param name="product">Product entity</param>
    /// <returns>Product DTO</returns>
    private static ProductDto MapToDto(Product product)
    {
        return new ProductDto
        {
            Id = product.Id,
            Name = product.Name,
            Description = product.Description,
            Price = product.Price,
            Sku = product.Sku,
            StockQuantity = product.StockQuantity,
            CategoryId = product.CategoryId,
            CategoryName = product.Category?.Name ?? string.Empty,
            CreatedAt = product.CreatedAt,
            UpdatedAt = product.UpdatedAt
        };
    }
}
