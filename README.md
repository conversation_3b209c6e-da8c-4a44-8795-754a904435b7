# Simple API - .NET 9 Web API with Entity Framework and SQL Server

A clean, well-structured Web API built with .NET 9, Entity Framework Core, and SQL Server, following best practices and clean architecture principles.

## 🏗️ Architecture

This solution follows Clean Architecture principles with the following layers:

- **SimpleApi.Domain** - Core business entities and interfaces
- **SimpleApi.Application** - Business logic, services, and DTOs
- **SimpleApi.Infrastructure** - Data access, repositories, and external concerns
- **SimpleApi.Api** - Web API controllers and configuration

## 🚀 Features

- **RESTful API** with full CRUD operations for Products and Categories
- **Entity Framework Core** with SQL Server provider
- **Repository Pattern** with Unit of Work
- **Dependency Injection** throughout the application
- **Global Exception Handling** middleware
- **Swagger/OpenAPI** documentation
- **Docker & Docker Compose** support
- **Health Check** endpoint
- **Structured Logging**
- **Data Seeding** with sample data
- **Soft Delete** implementation
- **Input Validation** with Data Annotations

## 🛠️ Technologies Used

- .NET 9
- ASP.NET Core Web API
- Entity Framework Core 9
- SQL Server 2022
- Docker & Docker Compose
- Swagger/OpenAPI
- Serilog (structured logging)

## 📋 Prerequisites

- [.NET 9 SDK](https://dotnet.microsoft.com/download/dotnet/9.0)
- [Docker Desktop](https://www.docker.com/products/docker-desktop)
- [SQL Server](https://www.microsoft.com/en-us/sql-server/sql-server-downloads) (if running without Docker)

## 🚀 Getting Started

### Option 1: Run with Docker Compose (Recommended)

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd simpleApi
   ```

2. **Start the application with Docker Compose**
   ```bash
   docker-compose up -d
   ```

3. **Access the API**
   - API: http://localhost:5000
   - Swagger UI: http://localhost:5000
   - Health Check: http://localhost:5000/health

### Option 2: Run Locally

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd simpleApi
   ```

2. **Update connection string** in `SimpleApi.Api/appsettings.json`
   ```json
   {
     "ConnectionStrings": {
       "DefaultConnection": "Server=localhost;Database=SimpleApiDb;Trusted_Connection=true;TrustServerCertificate=true;"
     }
   }
   ```

3. **Restore packages**
   ```bash
   dotnet restore
   ```

4. **Run database migrations**
   ```bash
   dotnet ef database update --project SimpleApi.Infrastructure --startup-project SimpleApi.Api
   ```

5. **Run the application**
   ```bash
   dotnet run --project SimpleApi.Api
   ```

## 📚 API Endpoints

### Products

- `GET /api/products` - Get all products
- `GET /api/products/{id}` - Get product by ID
- `GET /api/products/sku/{sku}` - Get product by SKU
- `GET /api/products/category/{categoryId}` - Get products by category
- `GET /api/products/low-stock?threshold=10` - Get low stock products
- `POST /api/products` - Create new product
- `PUT /api/products/{id}` - Update product
- `DELETE /api/products/{id}` - Delete product (soft delete)

### Categories

- `GET /api/categories` - Get all categories
- `GET /api/categories/{id}` - Get category by ID
- `GET /api/categories/name/{name}` - Get category by name

### Health Check

- `GET /health` - Health check endpoint

## 📊 Sample Data

The application automatically seeds the database with sample data including:

- **Categories**: Electronics, Books, Clothing, Home & Garden
- **Products**: Various products across different categories with realistic data

## 🔧 Configuration

### Environment Variables

When running with Docker Compose, the following environment variables are configured:

- `ASPNETCORE_ENVIRONMENT=Development`
- `ConnectionStrings__DefaultConnection` - SQL Server connection string

### Database Configuration

- **Server**: SQL Server 2022 (Docker container)
- **Database**: SimpleApiDb
- **Authentication**: SQL Server Authentication
- **Username**: sa
- **Password**: YourStrong@Passw0rd

## 🧪 Testing the API

### Using Swagger UI

1. Navigate to http://localhost:5000
2. Explore and test the API endpoints directly from the browser

### Using curl

```bash
# Get all products
curl -X GET "http://localhost:5000/api/products"

# Get product by ID
curl -X GET "http://localhost:5000/api/products/{product-id}"

# Create a new product
curl -X POST "http://localhost:5000/api/products" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "New Product",
    "description": "Product description",
    "price": 99.99,
    "sku": "NEW-001",
    "stockQuantity": 100,
    "categoryId": "{category-id}"
  }'
```

## 🏗️ Development

### Adding Migrations

```bash
dotnet ef migrations add MigrationName --project SimpleApi.Infrastructure --startup-project SimpleApi.Api
```

### Updating Database

```bash
dotnet ef database update --project SimpleApi.Infrastructure --startup-project SimpleApi.Api
```

### Building the Solution

```bash
dotnet build
```

### Running Tests

```bash
dotnet test
```

## 📝 Best Practices Implemented

- **SOLID Principles** - Single Responsibility, Open/Closed, Liskov Substitution, Interface Segregation, Dependency Inversion
- **Clean Architecture** - Separation of concerns with clear layer boundaries
- **Repository Pattern** - Abstraction over data access
- **Unit of Work Pattern** - Transaction management
- **Dependency Injection** - Loose coupling and testability
- **Global Exception Handling** - Centralized error handling
- **Input Validation** - Data annotations and model validation
- **Logging** - Structured logging throughout the application
- **API Documentation** - Comprehensive Swagger/OpenAPI documentation
- **Health Checks** - Application health monitoring
- **Soft Delete** - Data preservation with logical deletion

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.
