namespace SimpleApi.Application.DTOs;

/// <summary>
/// Data Transfer Object for Category
/// </summary>
public class CategoryDto
{
    /// <summary>
    /// Gets or sets the category ID
    /// </summary>
    public Guid Id { get; set; }

    /// <summary>
    /// Gets or sets the category name
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// Gets or sets the category description
    /// </summary>
    public string? Description { get; set; }

    /// <summary>
    /// Gets or sets the creation date
    /// </summary>
    public DateTime CreatedAt { get; set; }

    /// <summary>
    /// Gets or sets the last update date
    /// </summary>
    public DateTime UpdatedAt { get; set; }

    /// <summary>
    /// Gets or sets the number of products in this category
    /// </summary>
    public int ProductCount { get; set; }
}
