{"format": 1, "restore": {"E:\\temp\\simpleApi\\SimpleApi.Domain\\SimpleApi.Domain.csproj": {}}, "projects": {"E:\\temp\\simpleApi\\SimpleApi.Domain\\SimpleApi.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\temp\\simpleApi\\SimpleApi.Domain\\SimpleApi.Domain.csproj", "projectName": "SimpleApi.Domain", "projectPath": "E:\\temp\\simpleApi\\SimpleApi.Domain\\SimpleApi.Domain.csproj", "packagesPath": "E:\\Install\\Nuget\\", "outputPath": "E:\\temp\\simpleApi\\SimpleApi.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\Install\\VisualStudioShared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}}