{"format": 1, "restore": {"E:\\temp\\simpleApi\\SimpleApi.Api\\SimpleApi.Api.csproj": {}}, "projects": {"E:\\temp\\simpleApi\\SimpleApi.Api\\SimpleApi.Api.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\temp\\simpleApi\\SimpleApi.Api\\SimpleApi.Api.csproj", "projectName": "SimpleApi.Api", "projectPath": "E:\\temp\\simpleApi\\SimpleApi.Api\\SimpleApi.Api.csproj", "packagesPath": "E:\\Install\\Nuget\\", "outputPath": "E:\\temp\\simpleApi\\SimpleApi.Api\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\Install\\VisualStudioShared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"E:\\temp\\simpleApi\\SimpleApi.Application\\SimpleApi.Application.csproj": {"projectPath": "E:\\temp\\simpleApi\\SimpleApi.Application\\SimpleApi.Application.csproj"}, "E:\\temp\\simpleApi\\SimpleApi.Infrastructure\\SimpleApi.Infrastructure.csproj": {"projectPath": "E:\\temp\\simpleApi\\SimpleApi.Infrastructure\\SimpleApi.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.4, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.7, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[9.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "E:\\temp\\simpleApi\\SimpleApi.Application\\SimpleApi.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\temp\\simpleApi\\SimpleApi.Application\\SimpleApi.Application.csproj", "projectName": "SimpleApi.Application", "projectPath": "E:\\temp\\simpleApi\\SimpleApi.Application\\SimpleApi.Application.csproj", "packagesPath": "E:\\Install\\Nuget\\", "outputPath": "E:\\temp\\simpleApi\\SimpleApi.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\Install\\VisualStudioShared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"E:\\temp\\simpleApi\\SimpleApi.Domain\\SimpleApi.Domain.csproj": {"projectPath": "E:\\temp\\simpleApi\\SimpleApi.Domain\\SimpleApi.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "E:\\temp\\simpleApi\\SimpleApi.Domain\\SimpleApi.Domain.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\temp\\simpleApi\\SimpleApi.Domain\\SimpleApi.Domain.csproj", "projectName": "SimpleApi.Domain", "projectPath": "E:\\temp\\simpleApi\\SimpleApi.Domain\\SimpleApi.Domain.csproj", "packagesPath": "E:\\Install\\Nuget\\", "outputPath": "E:\\temp\\simpleApi\\SimpleApi.Domain\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\Install\\VisualStudioShared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}, "E:\\temp\\simpleApi\\SimpleApi.Infrastructure\\SimpleApi.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\temp\\simpleApi\\SimpleApi.Infrastructure\\SimpleApi.Infrastructure.csproj", "projectName": "SimpleApi.Infrastructure", "projectPath": "E:\\temp\\simpleApi\\SimpleApi.Infrastructure\\SimpleApi.Infrastructure.csproj", "packagesPath": "E:\\Install\\Nuget\\", "outputPath": "E:\\temp\\simpleApi\\SimpleApi.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["E:\\Install\\VisualStudioShared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"E:\\temp\\simpleApi\\SimpleApi.Domain\\SimpleApi.Domain.csproj": {"projectPath": "E:\\temp\\simpleApi\\SimpleApi.Domain\\SimpleApi.Domain.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.7, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.7, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.203/PortableRuntimeIdentifierGraph.json"}}}}}