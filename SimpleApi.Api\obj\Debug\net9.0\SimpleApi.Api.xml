<?xml version="1.0"?>
<doc>
    <assembly>
        <name>SimpleApi.Api</name>
    </assembly>
    <members>
        <member name="T:SimpleApi.Api.Controllers.CategoriesController">
            <summary>
            Categories API controller
            </summary>
        </member>
        <member name="M:SimpleApi.Api.Controllers.CategoriesController.#ctor(SimpleApi.Application.Services.ICategoryService,Microsoft.Extensions.Logging.ILogger{SimpleApi.Api.Controllers.CategoriesController})">
            <summary>
            Initializes a new instance of the <see cref="T:SimpleApi.Api.Controllers.CategoriesController"/> class
            </summary>
            <param name="categoryService">Category service</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:SimpleApi.Api.Controllers.CategoriesController.GetCategories(System.Threading.CancellationToken)">
            <summary>
            Gets all categories
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Collection of categories</returns>
        </member>
        <member name="M:SimpleApi.Api.Controllers.CategoriesController.GetCategory(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Gets a category by ID
            </summary>
            <param name="id">Category ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Category details</returns>
        </member>
        <member name="M:SimpleApi.Api.Controllers.CategoriesController.GetCategoryByName(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets a category by name
            </summary>
            <param name="name">Category name</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Category details</returns>
        </member>
        <member name="T:SimpleApi.Api.Controllers.HealthController">
            <summary>
            Health check controller
            </summary>
        </member>
        <member name="M:SimpleApi.Api.Controllers.HealthController.Get">
            <summary>
            Health check endpoint
            </summary>
            <returns>Health status</returns>
        </member>
        <member name="T:SimpleApi.Api.Controllers.ProductsController">
            <summary>
            Products API controller
            </summary>
        </member>
        <member name="M:SimpleApi.Api.Controllers.ProductsController.#ctor(SimpleApi.Application.Services.IProductService,Microsoft.Extensions.Logging.ILogger{SimpleApi.Api.Controllers.ProductsController})">
            <summary>
            Initializes a new instance of the <see cref="T:SimpleApi.Api.Controllers.ProductsController"/> class
            </summary>
            <param name="productService">Product service</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:SimpleApi.Api.Controllers.ProductsController.GetProducts(System.Threading.CancellationToken)">
            <summary>
            Gets all products
            </summary>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Collection of products</returns>
        </member>
        <member name="M:SimpleApi.Api.Controllers.ProductsController.GetProduct(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Gets a product by ID
            </summary>
            <param name="id">Product ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Product details</returns>
        </member>
        <member name="M:SimpleApi.Api.Controllers.ProductsController.GetProductsByCategory(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Gets products by category
            </summary>
            <param name="categoryId">Category ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Collection of products in the specified category</returns>
        </member>
        <member name="M:SimpleApi.Api.Controllers.ProductsController.GetProductBySku(System.String,System.Threading.CancellationToken)">
            <summary>
            Gets a product by SKU
            </summary>
            <param name="sku">Product SKU</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Product details</returns>
        </member>
        <member name="M:SimpleApi.Api.Controllers.ProductsController.GetLowStockProducts(System.Int32,System.Threading.CancellationToken)">
            <summary>
            Gets products with low stock
            </summary>
            <param name="threshold">Stock threshold (default: 10)</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Collection of products with low stock</returns>
        </member>
        <member name="M:SimpleApi.Api.Controllers.ProductsController.CreateProduct(SimpleApi.Application.DTOs.CreateProductDto,System.Threading.CancellationToken)">
            <summary>
            Creates a new product
            </summary>
            <param name="createProductDto">Product creation data</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Created product</returns>
        </member>
        <member name="M:SimpleApi.Api.Controllers.ProductsController.UpdateProduct(System.Guid,SimpleApi.Application.DTOs.UpdateProductDto,System.Threading.CancellationToken)">
            <summary>
            Updates an existing product
            </summary>
            <param name="id">Product ID</param>
            <param name="updateProductDto">Product update data</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>Updated product</returns>
        </member>
        <member name="M:SimpleApi.Api.Controllers.ProductsController.DeleteProduct(System.Guid,System.Threading.CancellationToken)">
            <summary>
            Deletes a product
            </summary>
            <param name="id">Product ID</param>
            <param name="cancellationToken">Cancellation token</param>
            <returns>No content if successful</returns>
        </member>
        <member name="T:SimpleApi.Api.Middleware.GlobalExceptionHandlingMiddleware">
            <summary>
            Global exception handling middleware
            </summary>
        </member>
        <member name="M:SimpleApi.Api.Middleware.GlobalExceptionHandlingMiddleware.#ctor(Microsoft.AspNetCore.Http.RequestDelegate,Microsoft.Extensions.Logging.ILogger{SimpleApi.Api.Middleware.GlobalExceptionHandlingMiddleware})">
            <summary>
            Initializes a new instance of the <see cref="T:SimpleApi.Api.Middleware.GlobalExceptionHandlingMiddleware"/> class
            </summary>
            <param name="next">Next request delegate</param>
            <param name="logger">Logger</param>
        </member>
        <member name="M:SimpleApi.Api.Middleware.GlobalExceptionHandlingMiddleware.InvokeAsync(Microsoft.AspNetCore.Http.HttpContext)">
            <summary>
            Invokes the middleware
            </summary>
            <param name="context">HTTP context</param>
            <returns>Task representing the asynchronous operation</returns>
        </member>
        <member name="M:SimpleApi.Api.Middleware.GlobalExceptionHandlingMiddleware.HandleExceptionAsync(Microsoft.AspNetCore.Http.HttpContext,System.Exception)">
            <summary>
            Handles exceptions and returns appropriate HTTP responses
            </summary>
            <param name="context">HTTP context</param>
            <param name="exception">Exception that occurred</param>
            <returns>Task representing the asynchronous operation</returns>
        </member>
        <member name="T:SimpleApi.Api.Middleware.ErrorResponse">
            <summary>
            Error response model
            </summary>
        </member>
        <member name="P:SimpleApi.Api.Middleware.ErrorResponse.Message">
            <summary>
            Gets or sets the error message
            </summary>
        </member>
        <member name="P:SimpleApi.Api.Middleware.ErrorResponse.StatusCode">
            <summary>
            Gets or sets the HTTP status code
            </summary>
        </member>
        <member name="P:SimpleApi.Api.Middleware.ErrorResponse.Timestamp">
            <summary>
            Gets or sets the timestamp when the error occurred
            </summary>
        </member>
    </members>
</doc>
