using SimpleApi.Domain.Entities;

namespace SimpleApi.Domain.Interfaces;

/// <summary>
/// Category repository interface
/// </summary>
public interface ICategoryRepository : IRepository<Category>
{
    /// <summary>
    /// Gets categories with their product counts
    /// </summary>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Collection of categories with product counts</returns>
    Task<IEnumerable<Category>> GetWithProductCountsAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// Gets a category by name
    /// </summary>
    /// <param name="name">Category name</param>
    /// <param name="cancellationToken">Cancellation token</param>
    /// <returns>Category with the specified name or null if not found</returns>
    Task<Category?> GetByNameAsync(string name, CancellationToken cancellationToken = default);
}
