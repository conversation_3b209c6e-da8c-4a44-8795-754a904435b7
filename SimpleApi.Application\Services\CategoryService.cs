using SimpleApi.Application.DTOs;
using SimpleApi.Domain.Entities;
using SimpleApi.Domain.Interfaces;

namespace SimpleApi.Application.Services;

/// <summary>
/// Category service implementation
/// </summary>
public class CategoryService : ICategoryService
{
    private readonly IUnitOfWork _unitOfWork;

    /// <summary>
    /// Initializes a new instance of the <see cref="CategoryService"/> class
    /// </summary>
    /// <param name="unitOfWork">Unit of work</param>
    public CategoryService(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork ?? throw new ArgumentNullException(nameof(unitOfWork));
    }

    /// <inheritdoc />
    public async Task<IEnumerable<CategoryDto>> GetAllCategoriesAsync(CancellationToken cancellationToken = default)
    {
        var categories = await _unitOfWork.Categories.GetWithProductCountsAsync(cancellationToken);
        return categories.Select(MapToDto);
    }

    /// <inheritdoc />
    public async Task<CategoryDto?> GetCategoryByIdAsync(Guid id, CancellationToken cancellationToken = default)
    {
        var category = await _unitOfWork.Categories.GetByIdAsync(id, cancellationToken);
        if (category == null)
            return null;

        // Load products to get count
        var categoryWithProducts = await _unitOfWork.Categories.GetWithProductCountsAsync(cancellationToken);
        var categoryWithCount = categoryWithProducts.FirstOrDefault(c => c.Id == id);
        
        return categoryWithCount != null ? MapToDto(categoryWithCount) : MapToDto(category);
    }

    /// <inheritdoc />
    public async Task<CategoryDto?> GetCategoryByNameAsync(string name, CancellationToken cancellationToken = default)
    {
        var category = await _unitOfWork.Categories.GetByNameAsync(name, cancellationToken);
        if (category == null)
            return null;

        // Load products to get count
        var categoryWithProducts = await _unitOfWork.Categories.GetWithProductCountsAsync(cancellationToken);
        var categoryWithCount = categoryWithProducts.FirstOrDefault(c => c.Id == category.Id);
        
        return categoryWithCount != null ? MapToDto(categoryWithCount) : MapToDto(category);
    }

    /// <summary>
    /// Maps a Category entity to a CategoryDto
    /// </summary>
    /// <param name="category">Category entity</param>
    /// <returns>Category DTO</returns>
    private static CategoryDto MapToDto(Category category)
    {
        return new CategoryDto
        {
            Id = category.Id,
            Name = category.Name,
            Description = category.Description,
            CreatedAt = category.CreatedAt,
            UpdatedAt = category.UpdatedAt,
            ProductCount = category.Products?.Count ?? 0
        };
    }
}
