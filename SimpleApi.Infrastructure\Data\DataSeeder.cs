using SimpleApi.Domain.Entities;

namespace SimpleApi.Infrastructure.Data;

/// <summary>
/// Data seeder for initial database data
/// </summary>
public static class DataSeeder
{
    /// <summary>
    /// Seeds the database with initial data
    /// </summary>
    /// <param name="context">Database context</param>
    /// <returns>Task representing the asynchronous operation</returns>
    public static async Task SeedAsync(ApplicationDbContext context)
    {
        if (context == null)
            throw new ArgumentNullException(nameof(context));

        // Check if data already exists
        if (context.Categories.Any() || context.Products.Any())
        {
            return; // Database has been seeded
        }

        // Seed Categories
        var categories = new List<Category>
        {
            new Category
            {
                Id = Guid.NewGuid(),
                Name = "Electronics",
                Description = "Electronic devices and accessories",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new Category
            {
                Id = Guid.NewGuid(),
                Name = "Books",
                Description = "Books and educational materials",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new Category
            {
                Id = Guid.NewGuid(),
                Name = "Clothing",
                Description = "Apparel and fashion items",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new Category
            {
                Id = Guid.NewGuid(),
                Name = "Home & Garden",
                Description = "Home improvement and gardening supplies",
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };

        await context.Categories.AddRangeAsync(categories);
        await context.SaveChangesAsync();

        // Seed Products
        var electronicsCategory = categories.First(c => c.Name == "Electronics");
        var booksCategory = categories.First(c => c.Name == "Books");
        var clothingCategory = categories.First(c => c.Name == "Clothing");
        var homeGardenCategory = categories.First(c => c.Name == "Home & Garden");

        var products = new List<Product>
        {
            // Electronics
            new Product
            {
                Id = Guid.NewGuid(),
                Name = "Smartphone",
                Description = "Latest model smartphone with advanced features",
                Price = 699.99m,
                Sku = "PHONE-001",
                StockQuantity = 50,
                CategoryId = electronicsCategory.Id,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new Product
            {
                Id = Guid.NewGuid(),
                Name = "Laptop",
                Description = "High-performance laptop for work and gaming",
                Price = 1299.99m,
                Sku = "LAPTOP-001",
                StockQuantity = 25,
                CategoryId = electronicsCategory.Id,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new Product
            {
                Id = Guid.NewGuid(),
                Name = "Wireless Headphones",
                Description = "Premium wireless headphones with noise cancellation",
                Price = 199.99m,
                Sku = "HEADPHONE-001",
                StockQuantity = 100,
                CategoryId = electronicsCategory.Id,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },

            // Books
            new Product
            {
                Id = Guid.NewGuid(),
                Name = "Clean Code",
                Description = "A Handbook of Agile Software Craftsmanship",
                Price = 39.99m,
                Sku = "BOOK-001",
                StockQuantity = 75,
                CategoryId = booksCategory.Id,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new Product
            {
                Id = Guid.NewGuid(),
                Name = "Design Patterns",
                Description = "Elements of Reusable Object-Oriented Software",
                Price = 49.99m,
                Sku = "BOOK-002",
                StockQuantity = 60,
                CategoryId = booksCategory.Id,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },

            // Clothing
            new Product
            {
                Id = Guid.NewGuid(),
                Name = "Cotton T-Shirt",
                Description = "Comfortable cotton t-shirt in various colors",
                Price = 19.99m,
                Sku = "SHIRT-001",
                StockQuantity = 200,
                CategoryId = clothingCategory.Id,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new Product
            {
                Id = Guid.NewGuid(),
                Name = "Denim Jeans",
                Description = "Classic denim jeans with modern fit",
                Price = 79.99m,
                Sku = "JEANS-001",
                StockQuantity = 150,
                CategoryId = clothingCategory.Id,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },

            // Home & Garden
            new Product
            {
                Id = Guid.NewGuid(),
                Name = "Garden Hose",
                Description = "50ft expandable garden hose with spray nozzle",
                Price = 29.99m,
                Sku = "HOSE-001",
                StockQuantity = 80,
                CategoryId = homeGardenCategory.Id,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },
            new Product
            {
                Id = Guid.NewGuid(),
                Name = "LED Light Bulbs",
                Description = "Energy-efficient LED light bulbs pack of 4",
                Price = 24.99m,
                Sku = "BULB-001",
                StockQuantity = 300,
                CategoryId = homeGardenCategory.Id,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            },

            // Low stock item for testing
            new Product
            {
                Id = Guid.NewGuid(),
                Name = "Premium Tablet",
                Description = "High-end tablet with stylus support",
                Price = 899.99m,
                Sku = "TABLET-001",
                StockQuantity = 5, // Low stock for testing
                CategoryId = electronicsCategory.Id,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            }
        };

        await context.Products.AddRangeAsync(products);
        await context.SaveChangesAsync();
    }
}
