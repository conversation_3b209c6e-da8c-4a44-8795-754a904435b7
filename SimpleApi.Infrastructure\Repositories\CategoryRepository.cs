using Microsoft.EntityFrameworkCore;
using SimpleApi.Domain.Entities;
using SimpleApi.Domain.Interfaces;
using SimpleApi.Infrastructure.Data;

namespace SimpleApi.Infrastructure.Repositories;

/// <summary>
/// Category repository implementation
/// </summary>
public class CategoryRepository : Repository<Category>, ICategoryRepository
{
    /// <summary>
    /// Initializes a new instance of the <see cref="CategoryRepository"/> class
    /// </summary>
    /// <param name="context">Database context</param>
    public CategoryRepository(ApplicationDbContext context) : base(context)
    {
    }

    /// <inheritdoc />
    public async Task<IEnumerable<Category>> GetWithProductCountsAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(c => c.Products)
            .ToListAsync(cancellationToken);
    }

    /// <inheritdoc />
    public async Task<Category?> GetByNameAsync(string name, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(name))
            return null;

        return await _dbSet
            .FirstOrDefaultAsync(c => c.Name == name, cancellationToken);
    }
}
